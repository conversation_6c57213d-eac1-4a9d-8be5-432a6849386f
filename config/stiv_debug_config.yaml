# STIV调试配置文件
# 用于测试分析线生成和可视化修复

# 全局调试配置
debug:
  save_debug_images: true
  log_level: "DEBUG"

# 文件目录相关配置
output_dir: "data/output"
result_csv: "flow_speed_results.csv"
pixel_to_meter: 0.0084

# STIV算法参数
stiv_params:
  # 坐标系统配置
  coordinate_system:
    type: "pixel_distance"
    calibration:
      pixels:
        - [590, 685]
        - [125, 585]
        - [950, 1050]
        - [1110, 615]
      distances:
        "(0,1)": 74.5
        "(0,2)": 54.6
        "(0,3)": 38.4
        "(1,2)": 127.4
        "(1,3)": 68.5
        "(2,3)": 86.7

  # 分析线配置
  analysis_lines:
    mode: "adaptive"
    flow_direction: 135.0  # 水流方向135°
    line_count: 3          # 3条分析线
    line_spacing: 50       # 线间距50像素
    line_length: 1000      # 线长度1000像素
    center_point: [800, 300]  # 主中心点[800, 300]
    lines: null
    lines_range: null

  # 算法参数
  algorithm:
    window_shape: [51, 51]
    filter_window: 64
    overlap: 0
    polar_filter_width: 10
    ksize: 7
    method: "fft"

# 预处理配置
preprocessing:
  rotate_image: false
  pre_roi: null
  roi: null
  resolution: 1.0
  image_correction:
    apply: false
    k1: 0.0
    c: 0.0
    f: 0.0
