# STIV算法技术分析文档

## 算法概述

STIV (Space-Time Image Velocimetry) 是一种基于时空图像分析的流速测量算法，通过分析视频序列中特定线条上的像素变化模式来估算水流速度。

## 核心原理

### 理论基础
STIV算法基于以下物理原理：
1. **时空图像构建**：将视频中每一帧的特定水平线提取出来，按时间顺序排列形成时空图像
2. **模式识别**：水流运动在时空图像中形成特定的倾斜条纹模式
3. **角度计算**：条纹的倾斜角度与流速成正比关系
4. **速度转换**：通过几何关系将角度转换为实际流速

### 数学模型
```
velocity = tan(angle) * fps / ppm / resolution
```
其中：
- `angle`: 时空图像中条纹的倾斜角度（弧度）
- `fps`: 视频帧率
- `ppm`: 像素每米比例
- `resolution`: 分辨率缩放因子

## 代码架构分析

### 主要类和组件

#### 1. STIV类 (`lib/awive/awive/algorithms/sti.py`)
**核心职责**：STIV算法的主要实现类

**关键属性**：
```python
class STIV:
    def __init__(self, config, loader, formatter, lines, images_dp=None):
        self.lines = [int(line * formatter.resolution) for line in lines]
        self.lines_range = [(int(lrange[0] * formatter.resolution), 
                            int(lrange[1] * formatter.resolution)) 
                           for lrange in config.lines_range]
        self.stis_qnt = len(lines)  # STI图像数量
        self._filter_win = w_mn * w_mn.T  # 滤波窗口
        self._polar_filter_width = config.polar_filter_width
```

#### 2. 配置类 (`lib/awive/awive/config.py`)
**STIV配置参数**：
```python
class Stiv(BaseModel):
    window_shape: tuple[int, int] = (51, 51)  # GMT方法的窗口大小
    filter_window: int                        # 滤波窗口大小
    overlap: int = 31                         # GMT方法的重叠度
    ksize: int = 7                           # GMT方法的核大小
    polar_filter_width: int                   # 极坐标滤波宽度
    lines_range: list[tuple[int, int]]        # 每条线的X坐标范围
```

## 算法处理流程

### 1. 时空图像生成 (`generate_st_images`)
```python
def generate_st_images(self) -> list[NDArray]:
    stis = [[] for _ in range(self.stis_qnt)]  # 初始化STI列表
    
    while self.loader.has_images():
        image = self.loader.read()
        # 图像预处理流程
        image = self.formatter.apply_distortion_correction(image)
        image = self.formatter.apply_roi_extraction(image)
        image = self.formatter.apply_resolution(image)
        
        # 提取每条线的像素行
        for i, (line_pos, line_range) in enumerate(zip(self.lines, self.lines_range)):
            row = image[line_pos, line_range[0]:line_range[1]]
            stis[i].append(row)
    
    return [np.array(sti) for sti in stis]
```

**关键步骤**：
1. **失真校正**：应用镜头失真校正
2. **ROI提取**：提取感兴趣区域
3. **分辨率调整**：应用分辨率缩放
4. **线条提取**：从每帧中提取指定位置的像素行
5. **STI构建**：将提取的行按时间顺序排列

### 2. STI滤波处理 (`_filter_sti`)
基于论文方法的改进滤波算法：

```python
def _filter_sti(self, sti: np.ndarray) -> NDArray:
    # 1. 裁剪和调整大小
    x = min(sti.shape)
    sti = sti[:, :x] if x == sti.shape[0] else sti[:x, :]
    sti = cv2.resize(sti, (600, 600), interpolation=cv2.INTER_LINEAR)
    
    # 2. 窗口函数滤波
    sti = self._conv2d(sti, self._filter_win)
    
    # 3. 傅里叶变换和频域滤波
    sti_ft = np.abs(np.fft.fftshift(np.fft.fft2(sti)))
    # 滤除垂直和水平模式
    c_x, c_y = int(sti_ft.shape[0]/2), int(sti_ft.shape[1]/2)
    sti_ft[c_x-self._vh_filter:c_x+self._vh_filter, :] = 0
    sti_ft[:, c_y-self._vh_filter:c_y+self._vh_filter] = 0
    
    # 4. 极坐标变换
    sti_ft_polar = self._to_polar_system(sti_ft)
    
    # 5. 极坐标域滤波
    polar_mask = self._generate_polar_mask(sti_ft_polar)
    sti_ft_polar = sti_ft_polar * polar_mask
    
    # 6. 逆变换
    sti_ft_filtered = self._to_polar_system(sti_ft_polar, "invert")
    sti_filtered = np.abs(np.fft.ifft2(np.fft.ifftshift(sti_ft_filtered)))
    
    return sti_filtered.astype(np.uint8)
```

### 3. 运动方向计算

#### 方法A：FFT方法 (`_calculate_mot_using_fft`)
```python
def _calculate_mot_using_fft(self, sti: NDArray) -> tuple[float, NDArray]:
    # 1. 边缘检测
    sti_canny = cv2.Canny(sti, 10, 10)
    
    # 2. 方形化处理
    sti_padd = self._squarify(sti_canny)
    
    # 3. FFT和极坐标变换
    sti_ft = np.abs(np.fft.fftshift(np.fft.fft2(sti_padd)))
    sti_ft_polar = self._to_polar_system(sti_ft)
    
    # 4. 积分谱分布分析
    isd = np.sum(sti_ft_polar.T, axis=0)
    freq, _ = self._get_main_freqs(isd)
    
    # 5. 角度和速度计算
    angle = 2 * math.pi * freq / sti_ft_polar.shape[0]
    velocity = self.get_velocity(angle)
    
    return velocity, mask
```

#### 方法B：GMT方法 (`_calculate_mot_using_gmt`)
```python
def _calculate_mot_using_gmt(self, sti: NDArray) -> tuple[float, NDArray]:
    window_width = int(self.config.window_shape[0] / 2)
    window_height = int(self.config.window_shape[1] / 2)
    
    angle_accumulated = 0
    c_total = 0
    
    # 滑动窗口处理
    for s in range(window_width, width - window_width, overlap):
        for e in range(window_height, height - window_height, overlap):
            image_window = sti[s-window_width:s+window_width, 
                              e-window_height:e+window_height]
            angle, coherence = self._process_sti(image_window)
            angle_accumulated += angle * coherence
            c_total += coherence
    
    mean_angle = angle_accumulated / c_total
    velocity = self.get_velocity(mean_angle)
    
    return velocity, mask
```

## 关键参数分析

### 1. polar_filter_width
**作用**：控制极坐标域滤波的宽度
**影响**：
- 较小值：对高频噪声敏感，适合快流速
- 较大值：平滑效果强，适合慢流速
- 推荐范围：10-25

### 2. lines_range
**作用**：定义每条分析线的X坐标范围
**配置策略**：
- 水平河道：固定范围 `[[x1,x2], [x1,x2], [x1,x2]]`
- 非水平河道：渐变范围 `[[x1,x2], [x1',x2'], [x1'',x2'']]`

### 3. filter_window
**作用**：控制窗口函数滤波的大小
**影响**：影响频域滤波的精度和计算复杂度

### 4. window_shape (GMT方法)
**作用**：定义GMT方法中滑动窗口的大小
**权衡**：窗口越大越稳定，但空间分辨率降低

## 算法优缺点分析

### 优点
1. **非接触式测量**：无需在水中放置设备
2. **多点同时测量**：可同时分析多条线的流速
3. **实时性好**：处理速度相对较快
4. **适应性强**：可处理不同水流条件

### 缺点
1. **对图像质量敏感**：需要清晰的水面纹理
2. **参数依赖性强**：需要针对场景调优参数
3. **假设限制**：假设流速在分析线上均匀
4. **光照影响**：强光反射或阴影会影响精度

## 核心问题确认

基于对当前实现的深入分析，确认以下四个核心问题需要解决：

### 4.1 坐标系统兼容性问题
**问题描述**：当前标定只支持像素位置+现实世界距离，无法兼容经纬度坐标系统
**现状分析**：
- 当前使用GCP（Ground Control Points）进行标定，格式为像素坐标+距离信息
- 虽然项目中有经纬度坐标的使用，但STIV算法本身不支持经纬度坐标系统
- 需要支持两种坐标系统的无缝切换

### 4.2 视频时间段选择问题
**问题描述**：对于较长视频，需要支持指定分析的开始和结束时间点
**现状分析**：
- 项目有视频裁剪功能（video_trimmer.py），但这是外部预处理
- STIV算法内部没有时间段选择功能，必须处理整个视频
- 需要在算法内部集成时间段选择功能

### 4.3 像素比例自动计算问题
**问题描述**：当前ppm（像素每米比例）需要手工配置，但理论上有了现场标定位置后应该可以自动计算
**现状分析**：
- 当前ppm需要手工配置（如"ppm": 0.01或"ppm": 30）
- 虽然有GCP标定信息（像素坐标+实际距离），但算法没有利用这些信息自动计算ppm
- 需要实现基于标定信息的ppm自动计算

### 4.4 分析线方向适配问题
**问题描述**：当前的'lines'和'lines_range'配置方式只适用于水流方向为水平的视频，无法适配不同角度的水流方向
**现状分析**：
- 当前lines_range配置方式假设水平流向
- 例如：`"lines_range": [[700, 1700], [700, 1700], [700, 1700]]`表示三条水平线都使用相同的X坐标范围
- 无法直观地适配不同角度的水流方向

## 重构方案

### 新架构设计
在`src/analysis_algorithms`目录下创建新的STIV实现：

```
src/analysis_algorithms/
├── stiv_v2/
│   ├── __init__.py
│   ├── coordinate_system.py      # 坐标系统管理
│   ├── time_manager.py          # 时间段管理
│   ├── analysis_lines.py        # 分析线管理
│   ├── stiv_core.py            # 核心STIV算法
│   ├── stiv_config.py          # 配置管理
│   ├── stiv_processor.py       # 主处理器
│   └── utils.py                # 工具函数
└── stiv_analyzer.py            # 统一接口
```

### 配置格式说明

STIV v2使用统一的YAML配置格式，详细的配置说明请参考：
- **配置文件格式指南**: `.augment/docs/config_format_guide.md`
- **示例配置文件**: `config/batch_config.yaml`

主要配置包括：
- **坐标系统配置**: 支持像素+距离和经纬度两种坐标系统
- **分析线配置**: 支持自适应和手动两种模式，新增center_point参数
- **算法参数**: 包含FFT和GMT两种运动计算方法
  - `method`: 运动计算方法配置，"fft"或"gmt"
  - 原始STIV算法只使用FFT方法（硬编码）
  - 当前实现增加了GMT方法选项，提供更多算法对比和研究可能性
  - 默认使用"fft"保持与原始算法的一致性
- **预处理参数**: 图像旋转、ROI、分辨率等预处理选项

### 实施计划
**第一阶段**：基础架构
- 创建模块结构
- 实现CoordinateSystem类
- 实现配置管理和向后兼容性

**第二阶段**：核心功能
- 实现TimeManager
- 实现AnalysisLines
- 重构STI生成算法

**第三阶段**：算法优化
- 移植和改进滤波算法
- 移植运动计算算法
- 性能优化

**第四阶段**：集成和测试
- 创建统一接口
- 编写测试用例
- 文档更新

## 历史AI建议

以下是之前分析中提出的优化建议，作为参考保留：

### 1. 慢流速场景优化
- 增大 `polar_filter_width` (18-25)
- 选择稳定的中间时段分析
- 优化ROI区域选择

### 2. 非水平河道适配
- 调整 `lines_range` 适应河道走向
- 考虑河道角度对速度分量的影响
- 优化分析线的位置选择

### 3. 噪声抑制
- 改进滤波算法
- 增强边缘检测的鲁棒性
- 多尺度分析融合

### 4. 实时性提升
- 优化FFT计算
- 并行处理多条STI
- 自适应参数调整

### 5. 模块化设计建议
```python
class STIVProcessor:
    def __init__(self, config):
        self.sti_generator = STIGenerator(config)
        self.sti_filter = STIFilter(config)
        self.motion_calculator = MotionCalculator(config)

    def process(self, video_path):
        stis = self.sti_generator.generate(video_path)
        filtered_stis = [self.sti_filter.filter(sti) for sti in stis]
        velocities = [self.motion_calculator.calculate(sti) for sti in filtered_stis]
        return velocities
```

### 6. 参数自适应
- 基于图像特征自动调整参数
- 多参数组合的效果评估
- 场景识别和参数推荐

### 7. 算法融合
- 结合FFT和GMT方法的优势
- 多尺度分析
- 置信度评估和结果融合

## STIV算法技术实现细节分析

基于当前重构版本的深入代码分析，以下是对STIV算法技术实现的详细解释：

### 1.1 STI形状计算逻辑

**问题**：当前处理720×1280分辨率的视频，为什么生成的STI形状是(900, 565)？

**解答**：
STI（Space-Time Image）的形状由两个维度决定：

1. **时间维度（第一维：900）**：
   - 来源：处理的视频帧数
   - 计算：根据配置的时间范围`['0:10', '0:40']`（30秒）和视频帧率计算
   - 公式：`帧数 = (结束时间 - 开始时间) × 帧率 = 30秒 × 30fps = 900帧`

2. **空间维度（第二维：565）**：
   - 来源：分析线的实际像素长度
   - 配置：`line_length: 600`像素
   - 实际：565像素（由于边界裁剪和坐标约束）
   - 原因：分析线在图像边界附近时，实际可提取的像素数会少于配置长度

**代码实现**：
```python
# 在STIGenerator.generate_stis()中
for i, line in enumerate(self.analysis_lines.lines):
    line_pixels = self.analysis_lines.get_line_pixels(line, frame)  # 提取565个像素
    stis[i].append(line_pixels)  # 每帧添加一行，最终900行
```

### 1.2 图像裁剪和调整逻辑

**问题**：解释为什么STI会从(900, 565)裁剪为(565, 565)的正方形，以及随后调整为(600, 600)的原因。

**解答**：

1. **裁剪为正方形 (900, 565) → (565, 565)**：
   - 目的：为后续的FFT和极坐标转换做准备
   - 逻辑：`x = min(sti.shape) = min(900, 565) = 565`
   - 实现：保留较小维度的尺寸，裁剪较大维度
   - 代码：`sti_cropped = sti[:, :x] if x == sti.shape[0] else sti[:x, :]`

2. **调整为标准尺寸 (565, 565) → (600, 600)**：
   - 目的：统一处理尺寸，符合论文标准
   - 原因：600×600是STIV论文中推荐的标准处理尺寸
   - 好处：确保滤波窗口和算法参数的一致性
   - 代码：`cv2.resize(sti_cropped, (600, 600), interpolation=cv2.INTER_LINEAR)`

**算法依据**：
- 正方形图像便于进行旋转不变的频域分析
- 标准尺寸确保滤波参数的有效性和可重复性

### 1.3 极坐标转换前的尺寸变化

**问题**：说明为什么在极坐标转换之前图像尺寸又变成了(537, 537)。

**解答**：

这个尺寸变化发生在频域滤波过程中：

1. **输入尺寸**：(600, 600) - 经过窗口滤波的STI
2. **FFT处理**：`np.fft.fft2(sti_windowed)` - 保持(600, 600)
3. **频域滤波**：去除垂直和水平模式后仍为(600, 600)
4. **极坐标转换输入**：(600, 600)
5. **极坐标转换输出**：(537, 537)

**尺寸变化原因**：
- OpenCV的`cv2.linearPolar`函数内部算法决定
- 计算公式：`max_radius = int(np.sqrt(row² + col²) / 2)`
- 对于600×600：`max_radius = int(np.sqrt(600² + 600²) / 2) = 424`
- 输出尺寸由OpenCV内部优化算法确定，通常为`(max_radius + padding, 角度分辨率)`

**目的**：
- 优化极坐标表示的精度和效率
- 避免边界效应和插值误差

### 1.4 极坐标转换原理

**问题**：详细解释什么是极坐标转换，在STIV算法中的作用和意义。

**解答**：

**极坐标转换定义**：
将笛卡尔坐标系(x, y)转换为极坐标系(r, θ)的数学变换：
- r = √(x² + y²)：距离中心的径向距离
- θ = arctan(y/x)：相对于水平轴的角度

**在STIV中的作用**：

1. **频域分析优化**：
   - 将频域中的径向和角度信息分离
   - 便于分析特定方向的频率成分
   - 简化角度相关的滤波操作

2. **运动方向提取**：
   - 水流运动在时空图像中形成倾斜条纹
   - 条纹在频域中表现为特定角度的能量集中
   - 极坐标转换将角度信息映射到一个维度上

3. **噪声抑制**：
   - 通过极坐标域的选择性滤波
   - 保留主要运动方向，抑制随机噪声
   - 提高信噪比和检测精度

**物理意义**：
- 径向分量(r)：对应频率的幅度
- 角度分量(θ)：对应运动的方向
- 极坐标滤波：选择性保留特定角度范围的频率成分

**代码实现**：
```python
def _to_polar_system(img: np.ndarray, option: str = "convert") -> NDArray:
    row, col = img.shape
    cent = (int(col / 2), int(row / 2))  # 中心点
    max_radius = int(np.sqrt(row**2 + col**2) / 2)  # 最大半径
    return cv2.linearPolar(img, cent, max_radius, flag)
```

### 1.5 调试图像含义分析

**问题**：解释'sti_00_filtered_20250904_093949.png'系列中4条分析线的original、filtered、result图像的实际物理含义和用途。

**解答**：

**1. Original图像（sti_XX_original_YYYYMMDD_HHMMSS.png）**：
- **物理含义**：原始时空图像，显示水流在特定分析线上的时间-空间变化模式
- **数据内容**：每行代表一个时间点，每列代表分析线上的一个空间位置
- **视觉特征**：
  - 水平条纹：静态物体或背景
  - 倾斜条纹：运动物体（水流、漂浮物等）
  - 条纹倾斜角度：与流速成正比
- **用途**：评估原始数据质量，识别明显的运动模式

**2. Filtered图像（sti_XX_filtered_YYYYMMDD_HHMMSS.png）**：
- **物理含义**：经过多级滤波处理的时空图像，去除了噪声和不相关的频率成分
- **处理过程**：
  - 窗口函数滤波：平滑处理
  - 频域滤波：去除垂直和水平模式
  - 极坐标滤波：选择性保留特定方向的信息
- **视觉特征**：
  - 更清晰的运动条纹
  - 减少的背景噪声
  - 增强的信噪比
- **用途**：验证滤波效果，确保保留了主要运动信息

**3. Result图像（sti_XX_result_YYYYMMDD_HHMMSS.png）**：
- **物理含义**：运动检测结果的可视化，叠加了算法检测到的运动方向信息
- **数据内容**：滤波后的STI + 角度掩码（mask）
- **视觉特征**：
  - 基础图像：滤波后的STI
  - 叠加元素：检测到的运动方向线条或区域
  - 颜色编码：不同颜色表示不同的运动方向或置信度
- **用途**：
  - 验证运动检测算法的准确性
  - 调试角度计算和方向识别
  - 评估算法在不同条件下的性能

**4条分析线的对比意义**：
- **空间分布**：不同位置的流速变化
- **一致性检验**：多条线结果的相互验证
- **流场分析**：整体流场的空间分布特征
- **异常检测**：识别局部异常或测量误差

### 1.6 配置参数解释

**问题**：解释当前配置文件中'k1'、'c'、'f'这三个参数的物理含义、作用机制和取值依据。

**解答**：

这三个参数位于`image_correction`配置中，用于镜头失真校正：

**1. k1参数（径向失真系数）**：
- **物理含义**：描述镜头径向失真的主要系数
- **作用机制**：
  - k1 > 0：桶形失真（图像边缘向内弯曲）
  - k1 < 0：枕形失真（图像边缘向外弯曲）
  - k1 = 0：无径向失真
- **数学模型**：`r_corrected = r * (1 + k1*r² + k2*r⁴ + ...)`
- **取值依据**：
  - 通过相机标定获得
  - 典型范围：-0.5 到 0.5
  - 当前配置：0.0（未启用失真校正）

**2. c参数（失真中心偏移）**：
- **物理含义**：失真中心相对于图像中心的偏移量
- **作用机制**：
  - 校正镜头光轴与图像中心的偏差
  - 影响失真校正的基准点位置
- **数学模型**：失真中心 = 图像中心 + c
- **取值依据**：
  - 相机标定过程中确定
  - 通常为像素单位的小数值
  - 当前配置：0.0（假设失真中心在图像中心）

**3. f参数（焦距参数）**：
- **物理含义**：相机的有效焦距，用于失真校正计算
- **作用机制**：
  - 确定失真校正的尺度因子
  - 影响径向距离的计算
  - 与像素尺寸和实际焦距相关
- **数学模型**：`r_normalized = r / f`
- **取值依据**：
  - 相机内参标定获得
  - 单位：像素
  - 典型值：几百到几千像素
  - 当前配置：0.0（未启用失真校正）

**配置状态分析**：
当前配置中`apply: false`，所有参数均为0.0，表示：
- 未启用镜头失真校正
- 假设使用的相机失真可忽略
- 或者视频已经过预处理校正

**启用建议**：
如果发现分析结果在图像边缘区域不准确，建议：
1. 进行相机标定获取准确参数
2. 启用失真校正：`apply: true`
3. 设置合适的k1、c、f值

---

**文档版本**: v1.1
**分析基础**: src/analysis_algorithms/stiv/stiv_core.py
**最后更新**: 2025-09-04
