"""
坐标系统管理模块

解决核心问题：
- 4.1 坐标系统兼容性问题：支持像素+距离和经纬度两种坐标系统
- 4.3 像素比例自动计算问题：基于标定信息自动计算ppm
"""

import math
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class CalibrationPoint:
    """标定点数据结构"""
    pixel: Tuple[int, int]  # 像素坐标 (x, y)
    lat: Optional[float] = None  # 纬度
    lon: Optional[float] = None  # 经度
    distance_to: Optional[Dict[int, float]] = None  # 到其他点的距离


class CoordinateSystem:
    """坐标系统管理类
    
    支持两种坐标系统：
    1. pixel_distance: 像素坐标 + 实际距离（向后兼容）
    2. geographic: 经纬度坐标系统
    """
    
    def __init__(self, coord_type: str, calibration_data: Dict):
        """初始化坐标系统
        
        Args:
            coord_type: 坐标系统类型，"pixel_distance" 或 "geographic"
            calibration_data: 标定数据
        """
        self.coord_type = coord_type
        self.calibration_data = calibration_data
        self.calibration_points: List[CalibrationPoint] = []
        self._ppm: Optional[float] = None
        
        self._parse_calibration_data()
    
    def _parse_calibration_data(self):
        """解析标定数据"""
        if self.coord_type == "pixel_distance":
            self._parse_pixel_distance_data()
        elif self.coord_type == "geographic":
            self._parse_geographic_data()
        else:
            raise ValueError(f"不支持的坐标系统类型: {self.coord_type}")
    
    def _parse_pixel_distance_data(self):
        """解析像素+距离标定数据（向后兼容格式）"""
        pixels = self.calibration_data.get("pixels", [])
        distances = self.calibration_data.get("distances", {})
        
        for i, pixel in enumerate(pixels):
            point = CalibrationPoint(pixel=tuple(pixel))
            
            # 解析距离信息
            distance_to = {}
            for key, dist in distances.items():
                # 解析键格式 "(i,j)"
                if key.startswith("(") and key.endswith(")"):
                    indices = key[1:-1].split(",")
                    if len(indices) == 2:
                        idx1, idx2 = int(indices[0]), int(indices[1])
                        if idx1 == i:
                            distance_to[idx2] = dist
                        elif idx2 == i:
                            distance_to[idx1] = dist
            
            point.distance_to = distance_to
            self.calibration_points.append(point)
    
    def _parse_geographic_data(self):
        """解析经纬度标定数据"""
        coordinates = self.calibration_data.get("coordinates", [])
        
        for coord in coordinates:
            point = CalibrationPoint(
                pixel=tuple(coord["pixel"]),
                lat=coord["lat"],
                lon=coord["lon"]
            )
            self.calibration_points.append(point)
    
    def calculate_ppm(self) -> float:
        """计算像素每米比例（pixels per meter）
        
        Returns:
            ppm值，单位：像素/米
        """
        if self._ppm is not None:
            return self._ppm
        
        if self.coord_type == "pixel_distance":
            self._ppm = self._calculate_ppm_from_distances()
        elif self.coord_type == "geographic":
            self._ppm = self._calculate_ppm_from_geographic()
        else:
            raise ValueError(f"无法为坐标系统类型 {self.coord_type} 计算ppm")
        
        logger.info(f"自动计算的ppm值: {self._ppm:.6f} 像素/米")
        return self._ppm
    
    def _calculate_ppm_from_distances(self) -> float:
        """基于像素+距离信息计算ppm"""
        ppm_values = []
        
        for i, point1 in enumerate(self.calibration_points):
            if point1.distance_to:
                for j, distance in point1.distance_to.items():
                    if j < len(self.calibration_points):
                        point2 = self.calibration_points[j]
                        
                        # 计算像素距离
                        pixel_distance = math.sqrt(
                            (point1.pixel[0] - point2.pixel[0]) ** 2 +
                            (point1.pixel[1] - point2.pixel[1]) ** 2
                        )
                        
                        # 计算ppm
                        if distance > 0:
                            ppm = pixel_distance / distance
                            ppm_values.append(ppm)
                            logger.debug(f"点{i}-点{j}: 像素距离={pixel_distance:.2f}, 实际距离={distance:.2f}m, ppm={ppm:.6f}")
        
        if not ppm_values:
            raise ValueError("无法从标定数据计算ppm：没有有效的距离信息")
        
        # 返回平均值
        return np.mean(ppm_values)
    
    def _calculate_ppm_from_geographic(self) -> float:
        """基于经纬度坐标计算ppm"""
        if len(self.calibration_points) < 2:
            raise ValueError("经纬度标定至少需要2个点")
        
        ppm_values = []
        
        for i in range(len(self.calibration_points)):
            for j in range(i + 1, len(self.calibration_points)):
                point1 = self.calibration_points[i]
                point2 = self.calibration_points[j]
                
                # 计算像素距离
                pixel_distance = math.sqrt(
                    (point1.pixel[0] - point2.pixel[0]) ** 2 +
                    (point1.pixel[1] - point2.pixel[1]) ** 2
                )
                
                # 计算地理距离（米）
                geo_distance = self._calculate_geographic_distance(
                    point1.lat, point1.lon, point2.lat, point2.lon
                )
                
                if geo_distance > 0:
                    ppm = pixel_distance / geo_distance
                    ppm_values.append(ppm)
                    logger.debug(f"点{i}-点{j}: 像素距离={pixel_distance:.2f}, 地理距离={geo_distance:.2f}m, ppm={ppm:.6f}")
        
        if not ppm_values:
            raise ValueError("无法从经纬度数据计算ppm：没有有效的地理距离")
        
        return np.mean(ppm_values)
    
    @staticmethod
    def _calculate_geographic_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两个经纬度点之间的距离（米）
        
        使用Haversine公式计算球面距离
        """
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Haversine公式
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = (math.sin(dlat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2)
        c = 2 * math.asin(math.sqrt(a))
        
        # 地球半径（米）
        earth_radius = 6371000
        
        return earth_radius * c
    
    def pixel_to_meters(self, pixel_distance: float) -> float:
        """将像素距离转换为米"""
        ppm = self.calculate_ppm()
        return pixel_distance / ppm
    
    def meters_to_pixels(self, meter_distance: float) -> float:
        """将米距离转换为像素"""
        ppm = self.calculate_ppm()
        return meter_distance * ppm
    
    def get_calibration_info(self) -> Dict:
        """获取标定信息摘要"""
        return {
            "coord_type": self.coord_type,
            "point_count": len(self.calibration_points),
            "ppm": self.calculate_ppm(),
            "calibration_points": [
                {
                    "pixel": point.pixel,
                    "lat": point.lat,
                    "lon": point.lon
                }
                for point in self.calibration_points
            ]
        }


def create_coordinate_system_from_legacy_config(config: Dict) -> CoordinateSystem:
    """从旧版配置创建坐标系统（向后兼容）
    
    Args:
        config: 旧版配置字典
        
    Returns:
        CoordinateSystem实例
    """
    # 检查是否有GCP配置
    gcp_config = config.get("dataset", {}).get("gcp", {})
    
    if gcp_config.get("apply", False):
        # 使用GCP配置
        calibration_data = {
            "pixels": gcp_config.get("pixels", []),
            "distances": gcp_config.get("distances", {})
        }
        return CoordinateSystem("pixel_distance", calibration_data)
    else:
        # 没有GCP配置，创建一个基本的坐标系统
        # 这种情况下需要手工指定ppm
        ppm = config.get("preprocessing", {}).get("ppm", 1.0)
        logger.warning(f"没有找到GCP标定信息，使用手工配置的ppm: {ppm}")
        
        # 创建一个虚拟的标定数据
        calibration_data = {
            "pixels": [[0, 0], [100, 0]],
            "distances": {"(0,1)": 100 / ppm}  # 根据ppm反推距离
        }
        coord_system = CoordinateSystem("pixel_distance", calibration_data)
        coord_system._ppm = ppm  # 直接设置ppm值
        return coord_system
