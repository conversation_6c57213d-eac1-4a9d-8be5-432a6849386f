"""
STIV主处理器

整合所有模块，提供统一的STIV处理接口
"""

import time
import logging
from typing import Dict, List, Optional, Callable
from pathlib import Path
import cv2
import numpy as np

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from utils.config_manager import STIVConfig
from .coordinate_system import CoordinateSystem
from .time_manager import TimeManager
from .analysis_lines import AnalysisLines
from .stiv_core import STIGenerator, STIFilter, MotionCalculator

logger = logging.getLogger(__name__)


class STIVProcessor:
    """STIV v2主处理器
    
    解决了原版本的四个核心问题：
    1. 坐标系统兼容性问题
    2. 视频时间段选择问题  
    3. 像素比例自动计算问题
    4. 分析线方向适配问题
    """
    
    def __init__(self, config: STIVConfig, video_path: str = ""):
        """初始化STIV处理器

        Args:
            config: STIV配置
            video_path: 视频文件路径
        """
        self.config = config
        self.video_path = video_path

        # 初始化各个组件
        self._init_components()

        logger.info("STIV处理器初始化完成")
        logger.info(f"视频路径: {video_path}")
        logger.info(f"算法方法: {config.algorithm.method}")
        logger.info(f"分析线模式: {config.analysis_lines.mode}")
        logger.info(f"坐标系统: {config.coordinate_system.type}")
    
    def _init_components(self):
        """初始化各个组件"""
        # 坐标系统
        self.coordinate_system = CoordinateSystem(
            self.config.coordinate_system.type,
            self.config.coordinate_system.calibration
        )

        # 时间管理器（暂时使用None，因为新配置结构中没有时间范围）
        self.time_manager = TimeManager(None, None)

        # 分析线管理器
        lines_config = {
            "mode": self.config.analysis_lines.mode,
            "flow_direction": self.config.analysis_lines.flow_direction,
            "line_count": self.config.analysis_lines.line_count,
            "line_spacing": self.config.analysis_lines.line_spacing,
            "line_length": self.config.analysis_lines.line_length,
            "center_point": self.config.analysis_lines.center_point,
            "lines": self.config.analysis_lines.lines,
            "lines_range": self.config.analysis_lines.lines_range
        }
        self.analysis_lines = AnalysisLines(self.config.analysis_lines.mode, lines_config)

        # 核心算法组件
        self.sti_generator = STIGenerator(self.coordinate_system, self.analysis_lines)
        self.sti_filter = STIFilter(self.config.algorithm)
        self.motion_calculator = MotionCalculator(self.coordinate_system, self.config.algorithm)
    
    def process(self, preprocessing_func: Optional[Callable] = None,
               method: Optional[str] = None, save_debug: bool = False) -> Dict[str, Dict[str, float]]:
        """执行STIV分析

        Args:
            preprocessing_func: 图像预处理函数
            method: 运动计算方法，"fft" 或 "gmt"，None时使用配置中的方法
            save_debug: 是否保存调试图像

        Returns:
            分析结果字典
        """
        # 使用配置中的方法（如果未指定）
        if method is None:
            method = self.config.algorithm.method

        logger.info("开始STIV分析...")
        logger.info(f"使用运动计算方法: {method}")
        total_start_time = time.time()
        
        # 1. 创建时间限制的加载器
        if not self.video_path:
            raise ValueError("未指定视频文件路径")

        loader = self.time_manager.create_loader(self.video_path)
        fps = loader.fps
        
        try:
            # 2. 生成STI
            logger.info("步骤1: 生成Space-Time Images")
            stis, first_original_frame = self.sti_generator.generate_stis(loader, preprocessing_func)

            if not stis:
                raise ValueError("没有生成任何STI图像")

            # 3. 处理每个STI
            results = {}
            velocities = []
            debug_data = []  # 存储调试数据，稍后统一处理

            for i, sti in enumerate(stis):
                logger.info(f"步骤2: 处理STI {i+1}/{len(stis)}")

                # 滤波
                filtered_sti = self.sti_filter.filter_sti(sti, i)

                # 计算运动
                if method == "fft":
                    velocity, mask = self.motion_calculator.calculate_velocity_fft(filtered_sti, fps)
                elif method == "gmt":
                    velocity, mask = self.motion_calculator.calculate_velocity_gmt(filtered_sti, fps)
                else:
                    raise ValueError(f"不支持的运动计算方法: {method}")

                velocities.append(velocity)
                results[str(i)] = {"velocity": velocity}

                logger.info(f"STI {i}: 速度 = {velocity:.3f} m/s")

                # 存储调试数据，稍后处理
                if save_debug:
                    debug_data.append({
                        'sti_id': i,
                        'original_sti': sti,
                        'filtered_sti': filtered_sti,
                        'mask': mask
                    })

            # 4. 保存调试图像（在所有流速计算完成后）
            if save_debug and debug_data:
                for data in debug_data:
                    self._save_debug_images(data['sti_id'], data['original_sti'],
                                          data['filtered_sti'], data['mask'],
                                          first_original_frame, velocities)

            # 5. 计算统计信息
            mean_velocity = np.mean(velocities) if velocities else 0.0
            results["summary"] = {
                "mean_velocity": mean_velocity,
                "velocity_std": np.std(velocities) if len(velocities) > 1 else 0.0,
                "line_count": len(velocities),
                "method": method
            }
            
            total_time = time.time() - total_start_time
            logger.info(f"STIV分析完成: 平均速度 = {mean_velocity:.3f} m/s, 耗时 {total_time:.2f}秒")
            
            return results
            
        finally:
            loader.release()
    


    def _save_debug_images(self, sti_id: int, original_sti: np.ndarray,
                          filtered_sti: np.ndarray, mask: np.ndarray,
                          first_original_frame: np.ndarray, velocities: List[float]):
        """保存调试图像"""
        from datetime import datetime
        import os

        debug_dir = Path("data/image/stiv_debug")
        debug_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存原始STI
        original_filename = f"sti_{sti_id:02d}_original_{timestamp}.png"
        original_path = debug_dir / original_filename
        cv2.imwrite(str(original_path), original_sti)
        file_size = os.path.getsize(original_path) / 1024  # KB
        # logger.debug(f"STI调试图像 - 原始STI:")
        # logger.debug(f"  类型: Space-Time Image (原始)")
        # logger.debug(f"  目的: 显示第{sti_id}条分析线的原始时空图像")
        # logger.debug(f"  尺寸: {original_sti.shape}")
        # logger.debug(f"  文件大小: {file_size:.1f} KB")
        # logger.debug(f"  保存路径: {original_path}")
        # logger.debug(f"  数据范围: [{original_sti.min():.1f}, {original_sti.max():.1f}]")

        # 保存滤波后的STI
        filtered_filename = f"sti_{sti_id:02d}_filtered_{timestamp}.png"
        filtered_path = debug_dir / filtered_filename
        cv2.imwrite(str(filtered_path), filtered_sti)
        file_size = os.path.getsize(filtered_path) / 1024  # KB
        # logger.debug(f"STI调试图像 - 滤波STI:")
        # logger.debug(f"  类型: Space-Time Image (滤波后)")
        # logger.debug(f"  目的: 显示第{sti_id}条分析线经过频域滤波后的STI")
        # logger.debug(f"  尺寸: {filtered_sti.shape}")
        # logger.debug(f"  文件大小: {file_size:.1f} KB")
        # logger.debug(f"  保存路径: {filtered_path}")
        # logger.debug(f"  滤波参数: filter_window={self.config.algorithm.filter_window}")

        # 保存带掩码的结果
        result_image = cv2.add(filtered_sti, mask.astype(np.uint8))
        result_filename = f"sti_{sti_id:02d}_result_{timestamp}.png"
        result_path = debug_dir / result_filename
        cv2.imwrite(str(result_path), result_image)
        file_size = os.path.getsize(result_path) / 1024  # KB
        # logger.debug(f"STI调试图像 - 运动检测结果:")
        # logger.debug(f"  类型: Space-Time Image (运动检测)")
        # logger.debug(f"  目的: 显示第{sti_id}条分析线的运动检测结果和角度掩码")
        # logger.debug(f"  尺寸: {result_image.shape}")
        # logger.debug(f"  文件大小: {file_size:.1f} KB")
        # logger.debug(f"  保存路径: {result_path}")
        # logger.debug(f"  算法方法: {self.config.algorithm.method}")

        # 只在第一个STI时保存分析线和ROI可视化图片
        if sti_id == 0 and first_original_frame is not None:
            self._save_coordinate_grid(debug_dir, first_original_frame, timestamp)
            self._save_analysis_lines_visualization(debug_dir, first_original_frame, timestamp, velocities)
            self._save_roi_visualization(debug_dir, first_original_frame, timestamp)

    def _save_analysis_lines_visualization(self, debug_dir: Path, original_frame: np.ndarray, timestamp: str, velocities: List[float] = None):
        """保存分析线可视化图片"""
        # 检查输入图像是否有效
        if original_frame is None or original_frame.size == 0:
            logger.error("分析线可视化：原始帧为空，跳过保存")
            return

        # 创建可视化图像
        vis_image = original_frame.copy()

        # 转换为彩色图像（如果是灰度图）
        if len(vis_image.shape) == 2:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)
        elif vis_image.shape[2] == 1:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)

        # 确保分析线已生成
        if not self.analysis_lines.lines:
            self.analysis_lines.generate_lines(original_frame.shape[:2])

        # 首先绘制配置的主中心点（红色标记）
        if self.config.analysis_lines.center_point:
            main_center_x, main_center_y = self.config.analysis_lines.center_point
            # 绘制大红色圆圈标记主中心点
            cv2.circle(vis_image, (main_center_x, main_center_y), 20, (0, 0, 255), 3)  # 红色空心圆
            cv2.circle(vis_image, (main_center_x, main_center_y), 8, (0, 0, 255), -1)  # 红色实心圆
            # 添加主中心点标签
            cv2.putText(vis_image, f"Main Center ({main_center_x},{main_center_y})",
                       (main_center_x + 25, main_center_y - 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

        # 绘制分析线
        for i, line in enumerate(self.analysis_lines.lines):
            # 使用绿色绘制分析线
            color = (0, 255, 0)  # 绿色 (BGR格式)

            # 绘制分析线（加粗）
            cv2.line(vis_image, line.start_point, line.end_point, color, 4)

            # 绘制起点（小圆圈）
            cv2.circle(vis_image, line.start_point, 6, color, -1)

            # 绘制终点（小圆圈）
            cv2.circle(vis_image, line.end_point, 6, color, -1)

            # 绘制分析线中心点（绿色圆圈，确保在线上）
            cv2.circle(vis_image, line.center_point, 6, color, -1)
            # 添加白色边框以便更清楚地看到
            cv2.circle(vis_image, line.center_point, 6, (255, 255, 255), 2)

            # 添加线的编号和详细信息
            label_x = line.center_point[0] + 15
            label_y = line.center_point[1] - 15

            # 确保标签在图像范围内
            if label_x + 200 > vis_image.shape[1]:
                label_x = line.center_point[0] - 200
            if label_y < 20:
                label_y = line.center_point[1] + 30

            cv2.putText(vis_image, f"Line{i} Center({line.center_point[0]},{line.center_point[1]})",
                       (label_x, label_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # 添加几何信息
            # actual_length = int(((line.end_point[0] - line.start_point[0])**2 +
            #                    (line.end_point[1] - line.start_point[1])**2)**0.5)
            # cv2.putText(vis_image, f"Length: {actual_length}px",
            #            (label_x, label_y + 20),
            #            cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # 添加流速标注（如果有流速数据）
            if velocities is not None and i < len(velocities):
                velocity = velocities[i]

                # 流速数值标注
                # velocity_text = f"Velocity: {velocity:.3f} m/s"
                # cv2.putText(vis_image, velocity_text,
                #            (label_x, label_y + 40),
                #            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)  # 黄色

                # 绘制流速方向箭头
                self._draw_velocity_arrow(vis_image, line, velocity)

                # 根据流速大小设置颜色编码
                velocity_color = self._get_velocity_color(velocity)
                cv2.circle(vis_image, line.center_point, 15, velocity_color, 3)

        # 保存图像
        filename = f"analysis_lines_visualization_{timestamp}.png"
        filepath = debug_dir / filename
        cv2.imwrite(str(filepath), vis_image)

        # 详细调试日志
        import os
        file_size = os.path.getsize(filepath) / 1024  # KB
        logger.debug(f"分析线可视化图像:")
        logger.debug(f"  类型: Analysis Lines Visualization")
        logger.debug(f"  目的: 显示STIV分析线在原始图像上的位置和方向")
        logger.debug(f"  尺寸: {vis_image.shape}")
        logger.debug(f"  文件大小: {file_size:.1f} KB")
        logger.debug(f"  保存路径: {filepath}")
        logger.debug(f"  配置参数:")
        logger.debug(f"    - 流向角度: {self.config.analysis_lines.flow_direction}°")
        logger.debug(f"    - 分析线数量: {self.config.analysis_lines.line_count}")
        logger.debug(f"    - 线间距: {self.config.analysis_lines.line_spacing}像素")
        logger.debug(f"    - 线长度: {self.config.analysis_lines.line_length}像素")
        logger.debug(f"    - 中心点: {self.config.analysis_lines.center_point}")
        logger.info(f"分析线可视化图片已保存: {filename}")

    def _draw_velocity_arrow(self, image: np.ndarray, line, velocity: float):
        """绘制流速方向箭头"""
        import math

        # 计算分析线的方向向量
        line_dx = line.end_point[0] - line.start_point[0]
        line_dy = line.end_point[1] - line.start_point[1]
        line_length = math.sqrt(line_dx**2 + line_dy**2)

        if line_length == 0:
            return  # 避免除零错误

        # 归一化分析线方向向量
        line_unit_x = line_dx / line_length
        line_unit_y = line_dy / line_length

        # 计算箭头长度（基于流速大小）
        arrow_length = min(60, max(25, abs(velocity) * 30))  # 25-60像素

        # 根据流速正负确定箭头方向
        if velocity >= 0:
            # 正流速：箭头指向分析线终点方向
            direction_x = line_unit_x
            direction_y = line_unit_y
            # 箭头起点选择在分析线起点附近
            arrow_start_x = line.start_point[0] + line_unit_x * 30
            arrow_start_y = line.start_point[1] + line_unit_y * 30
        else:
            # 负流速：箭头指向分析线起点方向
            direction_x = -line_unit_x
            direction_y = -line_unit_y
            # 箭头起点选择在分析线终点附近
            arrow_start_x = line.end_point[0] - line_unit_x * 30
            arrow_start_y = line.end_point[1] - line_unit_y * 30

        # 计算箭头终点
        arrow_end_x = arrow_start_x + direction_x * arrow_length
        arrow_end_y = arrow_start_y + direction_y * arrow_length

        # 选择箭头颜色（根据流速正负）
        if velocity >= 0:
            arrow_color = (0, 255, 255)  # 青色 - 正向流速
        else:
            arrow_color = (255, 0, 255)  # 紫色 - 负向流速

        # 绘制箭头主线
        cv2.arrowedLine(image, (int(arrow_start_x), int(arrow_start_y)),
                       (int(arrow_end_x), int(arrow_end_y)),
                       arrow_color, 3, tipLength=0.4)

        # 添加流速方向和数值标签
        label_x = int(arrow_end_x) + 15
        label_y = int(arrow_end_y) - 10

        # 确保标签在图像范围内
        if label_x + 100 > image.shape[1]:
            label_x = int(arrow_end_x) - 100
        if label_y < 20:
            label_y = int(arrow_end_y) + 25

        # 显示流速值和方向
        cv2.putText(image, f"velocity: {abs(velocity):.3f}m/s",
                   (label_x, label_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, arrow_color, 1)

    def _get_velocity_color(self, velocity: float):
        """根据流速大小返回颜色编码"""
        abs_velocity = abs(velocity)

        if abs_velocity < 0.1:
            return (128, 128, 128)  # 灰色 - 很慢
        elif abs_velocity < 0.3:
            return (0, 255, 0)      # 绿色 - 慢
        elif abs_velocity < 0.6:
            return (0, 255, 255)    # 黄色 - 中等
        elif abs_velocity < 1.0:
            return (0, 165, 255)    # 橙色 - 快
        else:
            return (0, 0, 255)      # 红色 - 很快

    def _save_roi_visualization(self, debug_dir: Path, original_frame: np.ndarray, timestamp: str):
        """保存ROI区域可视化图片"""
        # 检查输入图像是否有效
        if original_frame is None or original_frame.size == 0:
            logger.error("ROI可视化：原始帧为空，跳过保存")
            return

        # 创建可视化图像
        vis_image = original_frame.copy()

        # 转换为彩色图像（如果是灰度图）
        if len(vis_image.shape) == 2:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)
        elif vis_image.shape[2] == 1:
            vis_image = cv2.cvtColor(vis_image, cv2.COLOR_GRAY2BGR)

        roi_configured = False

        # 绘制pre_roi（如果存在）
        if self.config.preprocessing.pre_roi:
            pre_roi = self.config.preprocessing.pre_roi
            if len(pre_roi) == 2 and len(pre_roi[0]) == 2:
                # 格式: [[y1, x1], [y2, x2]]
                y1, x1 = pre_roi[0]
                y2, x2 = pre_roi[1]
                # 绘制蓝色边框
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 0), 3)  # 蓝色
                cv2.putText(vis_image, "Pre-ROI", (x1 + 10, y1 + 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
                roi_configured = True

        # 绘制roi（如果存在）
        if self.config.preprocessing.roi:
            roi = self.config.preprocessing.roi
            try:
                if len(roi) == 2 and len(roi[0]) == 2:
                    # 矩形格式: [[y1, x1], [y2, x2]]
                    y1, x1 = roi[0]
                    y2, x2 = roi[1]
                    # 绘制红色边框
                    cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 0, 255), 3)  # 红色
                    cv2.putText(vis_image, "ROI (Rectangle)", (x1 + 10, y1 + 60),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    roi_configured = True
                elif len(roi) >= 3:
                    # 多边形格式: [[x1, y1], [x2, y2], [x3, y3], ...]
                    # 坐标格式：[x, y] -> OpenCV格式
                    pts = np.array([[x, y] for x, y in roi], np.int32)

                    # 绘制多边形边界
                    cv2.polylines(vis_image, [pts], True, (0, 0, 255), 3)  # 红色边界

                    # 绘制半透明填充
                    overlay = vis_image.copy()
                    cv2.fillPoly(overlay, [pts], (0, 0, 255))  # 红色填充
                    cv2.addWeighted(vis_image, 0.8, overlay, 0.2, 0, vis_image)

                    # 绘制顶点和坐标标签
                    for i, (x, y) in enumerate(roi):
                        cv2.circle(vis_image, (x, y), 8, (0, 255, 255), -1)  # 黄色顶点
                        cv2.putText(vis_image, f"P{i+1}({x},{y})", (x + 15, y - 15),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                    # 添加标签
                    center_x = int(np.mean([point[0] for point in roi]))  # x坐标
                    center_y = int(np.mean([point[1] for point in roi]))  # y坐标
                    cv2.putText(vis_image, f"ROI (Polygon, {len(roi)} points)",
                               (center_x - 100, center_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    roi_configured = True
                else:
                    logger.warning(f"ROI格式不支持: {roi}")
            except Exception as e:
                logger.error(f"ROI可视化错误: {e}")
                logger.warning(f"ROI配置: {roi}")

        # 如果没有配置ROI，显示示例ROI（用于演示）
        if not roi_configured:
            # 使用yuwangling案例的示例ROI: [[75, 600], [75, 80], [750, 70], [1240, 140]]
            demo_pre_roi = [[75, 600], [750, 1240]]  # 示例Pre-ROI
            demo_roi = [[75, 80], [750, 140]]        # 示例ROI

            # 绘制示例Pre-ROI
            y1, x1 = demo_pre_roi[0]
            y2, x2 = demo_pre_roi[1]
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 0), 3)  # 蓝色
            cv2.putText(vis_image, "Demo Pre-ROI", (x1 + 10, y1 + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)

            # 绘制示例ROI
            y1, x1 = demo_roi[0]
            y2, x2 = demo_roi[1]
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 0, 255), 3)  # 红色
            cv2.putText(vis_image, "Demo ROI", (x1 + 10, y1 + 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

            # 添加说明文字
            cv2.putText(vis_image, "No ROI configured - showing demo ROI",
                       (50, vis_image.shape[0] - 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)  # 黄色

        # 保存图像
        filename = f"roi_visualization_{timestamp}.png"
        filepath = debug_dir / filename
        cv2.imwrite(str(filepath), vis_image)

        # 详细调试日志
        import os
        file_size = os.path.getsize(filepath) / 1024  # KB
        logger.debug(f"ROI可视化图像:")
        logger.debug(f"  类型: ROI Visualization")
        logger.debug(f"  目的: 显示预处理ROI区域在原始图像上的位置")
        logger.debug(f"  尺寸: {vis_image.shape}")
        logger.debug(f"  文件大小: {file_size:.1f} KB")
        logger.debug(f"  保存路径: {filepath}")
        logger.debug(f"  配置参数:")
        logger.debug(f"    - Pre-ROI: {self.config.preprocessing.pre_roi}")
        logger.debug(f"    - ROI: {self.config.preprocessing.roi}")
        logger.debug(f"    - 是否显示示例: {not roi_configured}")
        logger.info(f"ROI可视化图片已保存: {filename}")

    def _save_coordinate_grid(self, debug_dir: Path, original_frame: np.ndarray, timestamp: str):
        """保存坐标网格调试图片"""
        # 检查输入图像是否有效
        if original_frame is None or original_frame.size == 0:
            logger.error("坐标网格：原始帧为空，跳过保存")
            return

        # 创建网格图像
        grid_image = original_frame.copy()

        # 转换为彩色图像（如果是灰度图）
        if len(grid_image.shape) == 2:
            grid_image = cv2.cvtColor(grid_image, cv2.COLOR_GRAY2BGR)
        elif grid_image.shape[2] == 1:
            grid_image = cv2.cvtColor(grid_image, cv2.COLOR_GRAY2BGR)

        height, width = grid_image.shape[:2]

        # 网格参数
        grid_spacing_x = max(50, width // 20)   # 水平网格间距
        grid_spacing_y = max(50, height // 15)  # 垂直网格间距

        # 绘制垂直网格线
        for x in range(0, width, grid_spacing_x):
            cv2.line(grid_image, (x, 0), (x, height), (0, 255, 0), 1)  # 绿色网格线

            # 在顶部和底部添加x坐标标签
            if x % (grid_spacing_x * 2) == 0:  # 每隔一条线显示坐标
                cv2.putText(grid_image, f"{x}", (x + 5, 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(grid_image, f"{x}", (x + 5, height - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制水平网格线
        for y in range(0, height, grid_spacing_y):
            cv2.line(grid_image, (0, y), (width, y), (0, 255, 0), 1)  # 绿色网格线

            # 在左侧和右侧添加y坐标标签
            if y % (grid_spacing_y * 2) == 0:  # 每隔一条线显示坐标
                cv2.putText(grid_image, f"{y}", (5, y + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(grid_image, f"{y}", (width - 50, y + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制主要坐标轴（更粗的线）
        cv2.line(grid_image, (0, 0), (width, 0), (255, 255, 0), 2)      # 顶边 - 青色
        cv2.line(grid_image, (0, 0), (0, height), (255, 255, 0), 2)     # 左边 - 青色
        cv2.line(grid_image, (width-1, 0), (width-1, height), (255, 255, 0), 2)  # 右边 - 青色
        cv2.line(grid_image, (0, height-1), (width, height-1), (255, 255, 0), 2) # 底边 - 青色

        # 添加图像尺寸信息
        cv2.putText(grid_image, f"Image Size: {width} x {height}",
                   (width // 2 - 100, 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)

        # 添加网格间距信息
        cv2.putText(grid_image, f"Grid: {grid_spacing_x}x{grid_spacing_y} pixels",
                   (width // 2 - 100, height - 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # 添加坐标系说明
        cv2.putText(grid_image, "Origin (0,0) at top-left",
                   (10, height - 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(grid_image, "Format: (x, y)",
                   (10, height - 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        # 保存图像
        filename = f"coordinate_grid_{timestamp}.png"
        filepath = debug_dir / filename
        cv2.imwrite(str(filepath), grid_image)

        # 详细调试日志
        import os
        file_size = os.path.getsize(filepath) / 1024  # KB
        logger.debug(f"坐标网格调试图像:")
        logger.debug(f"  类型: Coordinate Grid")
        logger.debug(f"  目的: 提供像素坐标参考，用于验证分析线和ROI位置")
        logger.debug(f"  尺寸: {grid_image.shape}")
        logger.debug(f"  文件大小: {file_size:.1f} KB")
        logger.debug(f"  保存路径: {filepath}")
        logger.debug(f"  网格间距: {grid_spacing_x}x{grid_spacing_y} 像素")
        logger.debug(f"  图像尺寸: {width}x{height} 像素")
        logger.info(f"坐标网格调试图片已保存: {filename}")

    def get_analysis_info(self) -> Dict:
        """获取分析信息"""
        return {
            "coordinate_system": self.coordinate_system.get_calibration_info(),
            "time_range": self.time_manager.get_time_info(),
            "analysis_lines": self.analysis_lines.get_lines_info(),
            "config_summary": self.config.get_summary()
        }
    
    @classmethod
    def from_config_manager(cls, config_manager, video_path: str) -> 'STIVProcessor':
        """从配置管理器创建STIV处理器

        Args:
            config_manager: 配置管理器实例
            video_path: 视频文件路径

        Returns:
            STIVProcessor实例
        """
        # 配置日志级别
        try:
            from ...utils.logging_utils import configure_logging_from_config
            configure_logging_from_config(config_manager)
        except Exception as e:
            logger.warning(f"配置日志级别失败: {e}")

        stiv_config = config_manager.get_algorithm_config("stiv")
        if stiv_config is None:
            raise ValueError("配置文件中未找到STIV算法配置")

        return cls(stiv_config, video_path)


def create_preprocessing_function(preprocessing_config) -> Optional[Callable]:
    """创建预处理函数
    
    Args:
        preprocessing_config: 预处理配置
        
    Returns:
        预处理函数或None
    """
    if not preprocessing_config:
        return None
    
    def preprocess_image(image: np.ndarray) -> np.ndarray:
        """图像预处理函数"""
        processed = image.copy()
        
        # 旋转图像
        if preprocessing_config.rotate_image:
            processed = cv2.rotate(processed, cv2.ROTATE_90_CLOCKWISE)
        
        # 应用pre_roi
        if preprocessing_config.pre_roi:
            y1, x1 = preprocessing_config.pre_roi[0]
            y2, x2 = preprocessing_config.pre_roi[1]
            processed = processed[y1:y2, x1:x2]
        
        # 应用roi
        if preprocessing_config.roi:
            roi = preprocessing_config.roi
            if len(roi) == 2 and len(roi[0]) == 2:
                # 矩形ROI格式: [[y1, x1], [y2, x2]]
                y1, x1 = roi[0]
                y2, x2 = roi[1]
                processed = processed[y1:y2, x1:x2]
            elif len(roi) >= 3:
                # 多边形ROI格式: [[x1, y1], [x2, y2], [x3, y3], ...]
                # 创建多边形掩码
                mask = np.zeros(processed.shape[:2], dtype=np.uint8)
                # 坐标格式：[x, y] -> OpenCV需要的格式
                points = np.array([[x, y] for x, y in roi], dtype=np.int32)
                cv2.fillPoly(mask, [points], 255)

                # 应用掩码
                if len(processed.shape) == 3:
                    # 彩色图像
                    processed = cv2.bitwise_and(processed, processed, mask=mask)
                else:
                    # 灰度图像
                    processed = cv2.bitwise_and(processed, processed, mask=mask)

            else:
                logger.warning(f"ROI格式不支持: {roi}")
        
        # 应用分辨率缩放
        if preprocessing_config.resolution != 1.0:
            height, width = processed.shape[:2]
            new_height = int(height * preprocessing_config.resolution)
            new_width = int(width * preprocessing_config.resolution)
            processed = cv2.resize(processed, (new_width, new_height))
        
        # 图像校正（如果需要）
        if (preprocessing_config.image_correction and
            preprocessing_config.image_correction.get("apply", False)):
            # 这里可以添加镜头失真校正代码
            pass

        # 转换为灰度图像（STIV算法要求）
        if len(processed.shape) == 3:
            processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)

        return processed
    
    return preprocess_image


# 便捷函数
def analyze_video_with_stiv(video_path: str, config_path: str,
                           method: Optional[str] = None, save_debug: bool = False) -> Dict:
    """使用STIV分析视频的便捷函数

    Args:
        video_path: 视频文件路径
        config_path: 配置文件路径
        method: 运动计算方法，None时使用配置中的方法
        save_debug: 是否保存调试图像

    Returns:
        分析结果
    """
    from ...utils.config_manager import ConfigManager

    # 创建配置管理器
    config_manager = ConfigManager(config_path)

    # 创建处理器
    processor = STIVProcessor.from_config_manager(config_manager, video_path)

    # 创建预处理函数
    preprocess_func = create_preprocessing_function(processor.config.preprocessing)

    # 执行分析
    return processor.process(preprocess_func, method, save_debug)



