"""
分析线管理模块

解决核心问题：
- 4.4 分析线方向适配问题：支持任意角度的水流方向，提供直观的配置方式
"""

import math
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class AnalysisLine:
    """分析线数据结构"""
    line_id: int
    start_point: Tuple[int, int]  # 起始点 (x, y)
    end_point: Tuple[int, int]    # 结束点 (x, y)
    center_point: Tuple[int, int] # 中心点 (x, y)
    angle: float                  # 线的角度（弧度）
    length: int                   # 线的长度（像素）


class AnalysisLines:
    """分析线管理器
    
    支持两种模式：
    1. adaptive: 根据流向自动生成分析线
    2. manual: 手动指定分析线（向后兼容）
    """
    
    def __init__(self, mode: str, config: Dict):
        """初始化分析线管理器
        
        Args:
            mode: 模式，"adaptive" 或 "manual"
            config: 配置字典
        """
        self.mode = mode
        self.config = config
        self.lines: List[AnalysisLine] = []
        
        if mode == "adaptive":
            self._init_adaptive_mode()
        elif mode == "manual":
            self._init_manual_mode()
        else:
            raise ValueError(f"不支持的分析线模式: {mode}")
    
    def _init_adaptive_mode(self):
        """初始化自适应模式"""
        self.flow_direction = self.config.get("flow_direction", 0.0)  # 流向角度（度）
        self.line_count = self.config.get("line_count", 3)
        self.line_spacing = self.config.get("line_spacing", 50)
        self.line_length = self.config.get("line_length", 200)
        self.center_point = self.config.get("center_point", None)  # 中心点坐标 [x, y]

        logger.info(f"自适应分析线配置:")
        logger.info(f"  流向角度: {self.flow_direction}°")
        logger.info(f"  分析线数量: {self.line_count}")
        logger.info(f"  线间距: {self.line_spacing}像素")
        logger.info(f"  线长度: {self.line_length}像素")
        if self.center_point:
            logger.info(f"  指定中心点: {self.center_point}")
    
    def _init_manual_mode(self):
        """初始化手动模式（向后兼容）"""
        self.manual_lines = self.config.get("lines", [])
        self.manual_lines_range = self.config.get("lines_range", [])
        
        if len(self.manual_lines) != len(self.manual_lines_range):
            raise ValueError("手动模式下，lines和lines_range的数量必须一致")
        
        logger.info(f"手动分析线配置:")
        logger.info(f"  分析线位置: {self.manual_lines}")
        logger.info(f"  分析线范围: {self.manual_lines_range}")
    
    def generate_lines(self, image_shape: Tuple[int, int]) -> List[AnalysisLine]:
        """生成分析线

        Args:
            image_shape: 图像形状 (height, width)

        Returns:
            分析线列表
        """
        height, width = image_shape

        logger.debug(f"开始生成分析线: 图像尺寸={width}x{height}, 模式={self.mode}")
        logger.debug(f"分析线配置: 流向角度={self.flow_direction}°, 线数量={self.line_count}, 线间距={self.line_spacing}px, 线长度={self.line_length}px")
        if self.center_point:
            logger.debug(f"指定中心点: {self.center_point}")

        if self.mode == "adaptive":
            self.lines = self._generate_adaptive_lines(height, width)
        elif self.mode == "manual":
            self.lines = self._generate_manual_lines(height, width)

        logger.info(f"生成了 {len(self.lines)} 条分析线")

        # 输出详细的分析线信息
        for i, line in enumerate(self.lines):
            # 计算几何验证信息
            center_to_start = math.sqrt((line.start_point[0] - line.center_point[0])**2 +
                                      (line.start_point[1] - line.center_point[1])**2)
            center_to_end = math.sqrt((line.end_point[0] - line.center_point[0])**2 +
                                    (line.end_point[1] - line.center_point[1])**2)
            actual_length = math.sqrt((line.end_point[0] - line.start_point[0])**2 +
                                    (line.end_point[1] - line.start_point[1])**2)

            logger.debug(f"分析线{i}: 中心点({line.center_point[0]}, {line.center_point[1]}), "
                        f"起点({line.start_point[0]}, {line.start_point[1]}), "
                        f"终点({line.end_point[0]}, {line.end_point[1]}), "
                        f"角度{math.degrees(line.angle):.1f}°, 长度{actual_length:.1f}像素")
            logger.debug(f"  几何验证: 中心到起点距离={center_to_start:.1f}px, "
                        f"中心到终点距离={center_to_end:.1f}px, 期望距离={self.line_length/2:.1f}px")

        return self.lines
    
    def _generate_adaptive_lines(self, height: int, width: int) -> List[AnalysisLine]:
        """生成自适应分析线

        根据line_count的奇偶性和center_point配置生成分析线：
        - 奇数：一条线穿过中心点，其余线在两侧对称分布
        - 偶数：以中心点为基准画虚拟中线，在中线两侧对称分布
        """
        lines = []

        # 将流向角度转换为弧度
        flow_angle_rad = math.radians(self.flow_direction)
        logger.debug(f"流向角度: {self.flow_direction}° = {flow_angle_rad:.4f} 弧度")

        # 计算垂直于流向的方向（分析线方向）
        line_angle_rad = flow_angle_rad + math.pi / 2
        logger.debug(f"分析线角度: {math.degrees(line_angle_rad):.1f}° = {line_angle_rad:.4f} 弧度")

        # 确定主中心点
        if self.center_point:
            center_x, center_y = self.center_point
            logger.debug(f"使用指定主中心点: ({center_x}, {center_y})")
        else:
            center_x = width // 2
            center_y = height // 2
            logger.debug(f"使用图像中心点: ({center_x}, {center_y})")

        # 沿着流向方向分布分析线
        flow_dx = math.cos(flow_angle_rad)
        flow_dy = math.sin(flow_angle_rad)
        logger.debug(f"流向单位向量: dx={flow_dx:.4f}, dy={flow_dy:.4f}")

        # 根据line_count的奇偶性计算分析线位置
        if self.line_count % 2 == 1:
            # 奇数：一条线穿过中心点，其余线在两侧对称分布
            logger.debug(f"奇数分析线模式: {self.line_count}条线，中心线穿过主中心点")

            # 中心线的索引
            center_line_idx = self.line_count // 2
            logger.debug(f"中心线索引: {center_line_idx}")

            for i in range(self.line_count):
                # 计算相对于中心线的偏移
                offset_from_center = (i - center_line_idx) * self.line_spacing
                logger.debug(f"线{i}: 相对中心线偏移 = {offset_from_center}px")

                # 计算当前线的中心位置
                line_center_x = center_x + offset_from_center * flow_dx
                line_center_y = center_y + offset_from_center * flow_dy
                logger.debug(f"线{i}: 计算中心位置 = ({line_center_x:.1f}, {line_center_y:.1f})")

                # 生成分析线
                line = self._create_analysis_line(i, line_center_x, line_center_y,
                                                line_angle_rad, width, height)
                lines.append(line)
        else:
            # 偶数：以中心点为基准画虚拟中线，在中线两侧对称分布
            logger.debug(f"偶数分析线模式: {self.line_count}条线，在虚拟中线两侧对称分布")

            # 计算每侧的线数
            lines_per_side = self.line_count // 2

            for i in range(self.line_count):
                # 计算相对于虚拟中线的偏移
                if i < lines_per_side:
                    # 左侧（或上侧）
                    offset_from_center = -(lines_per_side - i - 0.5) * self.line_spacing
                else:
                    # 右侧（或下侧）
                    offset_from_center = (i - lines_per_side + 0.5) * self.line_spacing

                # 计算当前线的中心位置
                line_center_x = center_x + offset_from_center * flow_dx
                line_center_y = center_y + offset_from_center * flow_dy

                # 生成分析线
                line = self._create_analysis_line(i, line_center_x, line_center_y,
                                                line_angle_rad, width, height)
                lines.append(line)

        return lines

    def _create_analysis_line(self, line_id: int, center_x: float, center_y: float,
                            line_angle_rad: float, width: int, height: int) -> AnalysisLine:
        """创建单条分析线"""
        logger.debug(f"创建分析线{line_id}: 原始中心点=({center_x:.1f}, {center_y:.1f})")

        # 计算分析线的起点和终点（不限制中心点位置）
        half_length = self.line_length / 2
        line_dx = math.cos(line_angle_rad) * half_length
        line_dy = math.sin(line_angle_rad) * half_length

        logger.debug(f"分析线{line_id}: 半长度={half_length}px, 方向向量=({line_dx:.2f}, {line_dy:.2f})")

        # 计算理想的起点和终点
        ideal_start_x = center_x - line_dx
        ideal_start_y = center_y - line_dy
        ideal_end_x = center_x + line_dx
        ideal_end_y = center_y + line_dy

        logger.debug(f"分析线{line_id}: 理想起点=({ideal_start_x:.1f}, {ideal_start_y:.1f}), "
                    f"理想终点=({ideal_end_x:.1f}, {ideal_end_y:.1f})")

        # 不限制起点和终点在图像范围内，保持几何精确性
        # 这样可以确保分析线的几何属性正确
        start_x = int(round(ideal_start_x))
        start_y = int(round(ideal_start_y))
        end_x = int(round(ideal_end_x))
        end_y = int(round(ideal_end_y))

        # 保持中心点精确
        center_x_int = int(round(center_x))
        center_y_int = int(round(center_y))

        logger.debug(f"分析线{line_id}: 最终起点=({start_x}, {start_y}), "
                    f"最终终点=({end_x}, {end_y}), 最终中心点=({center_x_int}, {center_y_int})")

        # 几何验证
        actual_length = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)
        center_to_start = math.sqrt((start_x - center_x_int) ** 2 + (start_y - center_y_int) ** 2)
        center_to_end = math.sqrt((end_x - center_x_int) ** 2 + (end_y - center_y_int) ** 2)

        logger.debug(f"分析线{line_id}: 几何验证 - 实际长度={actual_length:.1f}px, "
                    f"中心到起点={center_to_start:.1f}px, 中心到终点={center_to_end:.1f}px")

        # 检查几何误差
        expected_half_length = self.line_length / 2
        start_error = abs(center_to_start - expected_half_length)
        end_error = abs(center_to_end - expected_half_length)

        if start_error > 1.0 or end_error > 1.0:
            logger.warning(f"分析线{line_id}: 几何误差较大 - 起点误差={start_error:.1f}px, "
                          f"终点误差={end_error:.1f}px")

        # 创建分析线
        return AnalysisLine(
            line_id=line_id,
            start_point=(start_x, start_y),
            end_point=(end_x, end_y),
            center_point=(center_x_int, center_y_int),
            angle=line_angle_rad,
            length=int(round(actual_length))
        )
    
    def _generate_manual_lines(self, height: int, width: int) -> List[AnalysisLine]:
        """生成手动指定的分析线（向后兼容）"""
        lines = []
        
        for i, (line_y, line_range) in enumerate(zip(self.manual_lines, self.manual_lines_range)):
            start_x, end_x = line_range
            
            # 确保坐标在有效范围内
            line_y = max(0, min(height - 1, line_y))
            start_x = max(0, min(width - 1, start_x))
            end_x = max(start_x, min(width - 1, end_x))
            
            # 创建水平分析线
            line = AnalysisLine(
                line_id=i,
                start_point=(start_x, line_y),
                end_point=(end_x, line_y),
                center_point=((start_x + end_x) // 2, line_y),
                angle=0.0,  # 水平线
                length=end_x - start_x
            )
            lines.append(line)
        
        return lines
    
    def get_line_pixels(self, line: AnalysisLine, image: np.ndarray) -> np.ndarray:
        """提取分析线上的像素值
        
        Args:
            line: 分析线
            image: 输入图像
            
        Returns:
            线上的像素值数组
        """
        if line.angle == 0.0:
            # 水平线，直接提取
            y = line.start_point[1]
            x_start = line.start_point[0]
            x_end = line.end_point[0]
            return image[y, x_start:x_end + 1]
        else:
            # 任意角度的线，使用线性插值
            return self._extract_line_pixels(line, image)
    
    def _extract_line_pixels(self, line: AnalysisLine, image: np.ndarray) -> np.ndarray:
        """提取任意角度线上的像素值"""
        x1, y1 = line.start_point
        x2, y2 = line.end_point
        
        # 计算线上的采样点
        num_points = max(abs(x2 - x1), abs(y2 - y1)) + 1
        x_coords = np.linspace(x1, x2, num_points)
        y_coords = np.linspace(y1, y2, num_points)
        
        # 使用双线性插值提取像素值
        pixels = []
        for x, y in zip(x_coords, y_coords):
            # 双线性插值
            x_int = int(x)
            y_int = int(y)
            x_frac = x - x_int
            y_frac = y - y_int
            
            # 确保坐标在图像范围内
            if (x_int >= 0 and x_int < image.shape[1] - 1 and 
                y_int >= 0 and y_int < image.shape[0] - 1):
                
                # 双线性插值
                pixel_value = (
                    image[y_int, x_int] * (1 - x_frac) * (1 - y_frac) +
                    image[y_int, x_int + 1] * x_frac * (1 - y_frac) +
                    image[y_int + 1, x_int] * (1 - x_frac) * y_frac +
                    image[y_int + 1, x_int + 1] * x_frac * y_frac
                )
                pixels.append(pixel_value)
            elif x_int >= 0 and x_int < image.shape[1] and y_int >= 0 and y_int < image.shape[0]:
                # 边界情况，直接取最近邻
                pixels.append(image[y_int, x_int])
        
        return np.array(pixels)
    
    def get_lines_info(self) -> Dict:
        """获取分析线信息"""
        return {
            "mode": self.mode,
            "line_count": len(self.lines),
            "lines": [
                {
                    "line_id": line.line_id,
                    "start_point": line.start_point,
                    "end_point": line.end_point,
                    "center_point": line.center_point,
                    "angle_degrees": math.degrees(line.angle),
                    "length": line.length
                }
                for line in self.lines
            ]
        }


def create_analysis_lines_from_legacy_config(config: dict) -> AnalysisLines:
    """从旧版配置创建分析线管理器（向后兼容）
    
    Args:
        config: 旧版配置字典
        
    Returns:
        AnalysisLines实例
    """
    # 检查是否有STIV配置
    stiv_config = config.get("stiv", {})
    lines = config.get("lines", [])
    lines_range = stiv_config.get("lines_range", [])
    
    if lines and lines_range:
        # 使用手动模式
        manual_config = {
            "lines": lines,
            "lines_range": lines_range
        }
        return AnalysisLines("manual", manual_config)
    else:
        # 创建默认的自适应配置
        adaptive_config = {
            "flow_direction": 0.0,  # 默认水平流向
            "line_count": 3,
            "line_spacing": 50,
            "line_length": 200
        }
        return AnalysisLines("adaptive", adaptive_config)
