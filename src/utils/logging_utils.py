import logging
import sys
from pathlib import Path

def setup_logger(name, log_file=None, level=logging.INFO):
    """
    设置日志器

    Args:
        name: 日志器名称
        log_file: 日志文件路径
        level: 日志级别

    Returns:
        logger: 配置好的日志器
    """
    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # 移除现有处理器（避免重复）
    if logger.handlers:
        logger.handlers = []

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)  # 确保处理器也设置正确的级别
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)  # 确保文件处理器也设置正确的级别
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def configure_logging_from_config(config_manager):
    """从配置管理器配置日志系统

    Args:
        config_manager: 配置管理器实例
    """
    log_level_str = config_manager.get_log_level().upper()
    log_level = getattr(logging, log_level_str, logging.INFO)

    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 确保根日志器有控制台处理器
    if not root_logger.handlers:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    else:
        # 更新现有处理器的级别
        for handler in root_logger.handlers:
            handler.setLevel(log_level)

    # 配置STIV相关模块的日志器
    stiv_modules = [
        'src.analysis_algorithms.stiv.analysis_lines',
        'src.analysis_algorithms.stiv.stiv_processor',
        'src.analysis_algorithms.stiv.stiv_core',
        'src.analysis_algorithms.stiv_analyzer'
    ]

    for module_name in stiv_modules:
        module_logger = logging.getLogger(module_name)
        module_logger.setLevel(log_level)

        # 确保模块日志器有处理器或者使用父日志器的处理器
        if not module_logger.handlers and not module_logger.propagate:
            module_logger.propagate = True

    # print(f"日志级别已设置为: {log_level_str}")
    return log_level